import { LanguageE } from '@/enums';
import { GetStaticPaths, GetStaticPropsContext } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export const getStaticTranslationsProps =
  (namespaces: string[] = []) =>
  async (context: GetStaticPropsContext) => {
    const { locale } = context;
    const translations = await serverSideTranslations(locale || LanguageE.JA, [
      ...namespaces,
      'components',
      'common',
    ]);

    return {
      props: {
        ...translations,
        id: '',
      },
    };
  };

export const getStaticPaths: GetStaticPaths = async () => {
  const paths: { params: { id: string } }[] = [
    {
      params: { id: 'id' },
    },
  ];

  return {
    paths,
    fallback: true,
  };
};
