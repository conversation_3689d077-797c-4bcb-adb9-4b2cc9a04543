import { Button } from '@/components/atoms';
import { Form } from '@/components/organisms/Form';
import { TFunction } from 'i18next';
import useColorSizeDetail from '../../hooks/useColorSizeDetail';
import InfoProduct from '../ColorList/ViewTable';

interface ColorSizeModalPropsI {
  t: TFunction<'products'>;
}

const ColorSizeModal = ({ t }: ColorSizeModalPropsI) => {
  const { form, formFields, detail, handleSubmit } = useColorSizeDetail(t);

  return (
    <div className="flex w-full flex-col gap-1">
      <InfoProduct
        data={[
          { label: t('partNumber'), value: '36-250134302-10' },
          {
            label: t('productName'),
            value: 'キッズ　配色ステッチ刺繍半袖Tシャツ',
          },
        ]}
      />
      <div className="flex w-full flex-col">
        <p className="font-bold">{t('beforeChange')}</p>
        <InfoProduct
          data={[
            { label: t('color'), value: '［White］' },
            { label: t('size'), value: '［S］' },
          ]}
        />
      </div>
      <div className="flex w-full flex-col">
        <p className="font-bold">{t('afterChange')}</p>
        <Form
          name="form"
          form={form}
          formFields={formFields}
          numOfColumns={1}
        />
      </div>
      <div className="mt-2 flex flex-row gap-2">
        <Button
          type="primary"
          danger
          label={t('update')}
          onClick={handleSubmit}
        />
        <Button type="primary" label={t('delete')} />
      </div>
      {detail && (
        <div className="mt-2 text-xs text-gray-500">
          {t('newRegistrationDateAndTime')}: {detail?.created_at}{' '}
          {detail?.created_by?.name ? `(${detail?.created_by?.name})` : ''}
          <br />
          {t('lastUpdated')}: {detail?.updated_at}{' '}
          {detail?.updated_by?.name ? `(${detail?.updated_by?.name})` : ''}
        </div>
      )}
    </div>
  );
};

export default ColorSizeModal;
