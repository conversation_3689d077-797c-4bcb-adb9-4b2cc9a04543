import { Button } from '@/components/atoms';
import { Form } from '@/components/organisms/Form';
import { TFunction } from 'i18next';
import useColorSizeDetail from '../../hooks/useColorSizeDetail';
import { StoreBudgetTypeI } from '../../types';
import ViewTable from '../ColorList/ViewTable';

interface ColorSizeModalPropsI {
  previousData: StoreBudgetTypeI;
  t: TFunction<'products'>;
  onClose?: () => void;
}

const ColorSizeModal = ({ t, previousData, onClose }: ColorSizeModalPropsI) => {
  const { form, formFields, detail, handleDelete, handleSubmit } =
    useColorSizeDetail(t);

  const handleClickUpdate = async () => {
    await handleSubmit();
    onClose?.();
  };

  const handleclickDelete = async () => {
    await handleDelete();
    onClose?.();
  };

  return (
    <div className="flex w-full flex-col gap-1">
      <ViewTable
        data={[
          { label: t('partNumber'), value: '36-250134302-10' },
          {
            label: t('productName'),
            value: 'キッズ　配色ステッチ刺繍半袖Tシャツ',
          },
        ]}
      />
      <div className="flex w-full flex-col">
        <p className="font-bold">{t('beforeChange')}</p>
        <ViewTable
          data={[
            {
              label: t('color'),
              value: previousData.pms_color?.code_name || '',
            },
            { label: t('size'), value: previousData.pms_size_code },
          ]}
        />
      </div>
      <div className="flex w-full flex-col">
        <p className="font-bold">{t('afterChange')}</p>
        <Form
          name="form"
          form={form}
          formFields={formFields}
          numOfColumns={1}
        />
      </div>
      <div className="mt-2 flex flex-row gap-2">
        <Button
          type="primary"
          danger
          label={t('update')}
          onClick={handleClickUpdate}
        />
        <Button
          type="primary"
          label={t('delete')}
          onClick={handleclickDelete}
        />
      </div>
      {detail && (
        <div className="mt-2 text-xs text-gray-500">
          {t('newRegistrationDateAndTime')}: {detail?.created_at}{' '}
          {detail?.created_by?.name ? `(${detail?.created_by?.name})` : ''}
          <br />
          {t('lastUpdated')}: {detail?.updated_at}{' '}
          {detail?.updated_by?.name ? `(${detail?.updated_by?.name})` : ''}
        </div>
      )}
    </div>
  );
};

export default ColorSizeModal;
