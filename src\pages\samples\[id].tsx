import { Button, Collapse, EditableTable } from '@/components/atoms';
import FileUploadList from '@/components/organisms/FileUploadList';
import { Form } from '@/components/organisms/Form';
import ImageUploadList from '@/components/organisms/ImageList';
import { FILE_TYPES, MAX_IMAGE_SIZE } from '@/constants/common';
import { ApiEndpointE } from '@/enums';
import { useSampleDetailInformation } from '@/features/sample-information/hooks/useSampleInformationDetail';
import { withMainLayout } from '@/hocs/withMainLayout';
import { DateHelper } from '@/utils/date';
import {
  getStaticPaths,
  getStaticTranslationsProps,
} from '@/utils/localization';
import { Form as AntdForm } from 'antd';
import { useTranslation } from 'next-i18next';
import router from 'next/router';
import { Fragment, useMemo, useState } from 'react';

interface SampleInformationDetailPagePropsI {
  type: 'create' | 'edit';
}

const SampleInformationDetailPage = ({
  type = 'edit',
}: SampleInformationDetailPagePropsI) => {
  const { t } = useTranslation('sample-information');
  const [activeKeys, setActiveKeys] = useState<string[]>([]);
  const [basicForm] = AntdForm.useForm();
  const [partnerForm] = AntdForm.useForm();

  const {
    data,
    basicFormFields,
    partnerFormFields,
    handleSubmit,
    handleCollapseAll,
    handleCollapseNone,
    handleDelete,
    dataTableMateria,
    columnsMaterial,
    setDataTableMateria,
  } = useSampleDetailInformation();
  const sample = data?.result;

  const initialValuesSample = useMemo(
    () => ({
      ...sample,
      pms_plan_datetime: sample?.pms_plan_datetime
        ? DateHelper.toDayjs(sample.pms_plan_datetime)
        : null,
      pms_sample_request_datetime: sample?.pms_sample_request_datetime
        ? DateHelper.toDayjs(sample.pms_sample_request_datetime)
        : null,
    }),
    [sample],
  );

  const collapseItems = useMemo(
    () => [
      {
        key: 'partner',
        label: t('partner'),
        children: (
          <Form
            name="partner"
            form={partnerForm}
            initialValues={initialValuesSample}
            formFields={partnerFormFields}
            numOfColumns={1}
          />
        ),
      },
      {
        key: 'material',
        label: t('material'),
        children: (
          <Fragment>
            <Button label={t('addMaterial')} className="mb-4" />
            <EditableTable
              pagination={false}
              data={dataTableMateria}
              columns={columnsMaterial}
              setData={setDataTableMateria}
            />
          </Fragment>
        ),
      },
      {
        key: 'sampleImage',
        label: t('sampleImage'),
        children: <ImageUploadList />,
      },
      {
        key: 'patternFiles',
        label: t('patternFiles'),
        children: (
          <FileUploadList
            initialFiles={[]}
            urlUpdate={ApiEndpointE.FILE_UPDATE}
            maxFileSizeMB={MAX_IMAGE_SIZE}
            allowedTypes={FILE_TYPES}
          />
        ),
      },
    ],
    [
      t,
      partnerForm,
      initialValuesSample,
      partnerFormFields,
      dataTableMateria,
      columnsMaterial,
      setDataTableMateria,
    ],
  );

  return (
    <Fragment>
      <Button
        label={t('return')}
        type="default"
        className="mb-4"
        onClick={() => router.back()}
      />
      <Form
        name="basic"
        form={basicForm}
        initialValues={initialValuesSample}
        formFields={basicFormFields}
        numOfColumns={1}
      />
      <div className="mt-4 mb-4 flex gap-2">
        {activeKeys.length > 0 ? (
          <Button
            label={t('closeAll')}
            onClick={() => handleCollapseNone(setActiveKeys)}
          />
        ) : (
          <Button
            label={t('openAll')}
            onClick={() => handleCollapseAll(setActiveKeys)}
          />
        )}
      </div>
      <Collapse
        activeKey={activeKeys}
        items={collapseItems}
        className="mb-4"
        onChange={(keys: string[] | string) =>
          setActiveKeys(Array.isArray(keys) ? keys : [keys])
        }
      />
      <div className="mt-4 flex gap-2">
        <Button
          type="primary"
          label={type === 'edit' ? t('update') : t('registration')}
          onClick={() => handleSubmit(basicForm, partnerForm)}
        />
        <Button
          hidden={type !== 'edit'}
          type="primary"
          danger
          label={t('delete')}
          onClick={() => handleDelete}
        />
      </div>
      {type === 'edit' && (
        <div className="mt-2 text-xs text-gray-500">
          {t('newRegistrationDateAndTime')}: {sample?.created_at}{' '}
          {sample?.created_by?.name ? `(${sample?.created_by?.name})` : ''}
          <br />
          {t('lastUpdated')}: {sample?.updated_at}{' '}
          {sample?.updated_by?.name ? `(${sample?.updated_by?.name})` : ''}
        </div>
      )}
    </Fragment>
  );
};

export { getStaticPaths };

export const getStaticProps = getStaticTranslationsProps([
  'sample-information',
]);

export default withMainLayout(SampleInformationDetailPage);
