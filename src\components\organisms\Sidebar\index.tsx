import { Input } from '@/components/atoms';
import {
  ExperimentOutlined,
  LaptopOutlined,
  MenuOutlined,
  SearchOutlined,
  SettingOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { Menu } from 'antd';
import { useTranslation } from 'next-i18next';
import { usePathname, useRouter } from 'next/navigation';
import styled from 'styled-components';

import { MenuItemType } from 'antd/es/menu/interface';
import { useEffect, useState } from 'react';

const StyledMenu = styled(Menu)`
  .ant-menu-item {
    /* background-color: #515055 !important; */
    /* margin-inline: auto !important; */
    /* z-index: 100 !important; */
  }

  .ant-menu-submenu-title {
    /* background-color: #151519 !important; */
  }

  .ant-menu-submenu {
    /* background-color: #151519 !important; */
  }

  .ant-menu-submenu {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
    border-bottom: 1px solid #29282d !important;
    border-radius: 0px !important;
  }

  .ant-menu .ant-menu-item-selected {
    .ant-menu-title-content {
      font-family: NotoSansJP;
      color: var(--button-accent-text) !important;
    }
  }

  .ant-menu-title-content {
    font-family: NotoSansJP;
    /* color: white !important; */
  }

  .ant-menu-submenu-open {
    /* background-color: #151519; */
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 20px !important;
    border-radius: 0px !important;
  }
`;

interface SidebarPropsI {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
}

const Sidebar = ({ collapsed, setCollapsed }: SidebarPropsI) => {
  const pathname = usePathname();
  const router = useRouter();
  const { t } = useTranslation('components');
  const [isMobileSize, setIsMobileSize] = useState(false);

  const menuItems: MenuItemType[] = [
    {
      key: '/samples',
      icon: <ExperimentOutlined className="!text-[18px]" />,
      label: t('sidebar.sampleInformationList'),
    },
    {
      key: '/menu-2',
      icon: <SearchOutlined className="!text-[18px]" />,
      label: t('sidebar.menu1'),
    },
    {
      key: '/menu-3',
      icon: <TeamOutlined className="!text-[18px]" />,
      label: t('sidebar.menu2'),
    },
    {
      key: '/menu-4',
      icon: <LaptopOutlined className="!text-[18px]" />,
      label: t('sidebar.menu3'),
    },
    {
      key: '/menu-5',
      icon: <SettingOutlined className="!text-[18px]" />,
      label: t('sidebar.menu4'),
    },
  ].map((menu) => ({
    ...menu,
    onClick: () => {
      router.push(menu.key);
    },
  }));

  const collapseMenu = () => {
    if (isMobileSize) {
      return;
    }

    return setCollapsed(!collapsed);
  };

  useEffect(() => {
    const handleResize = () => {
      const mdMinSize = 768;
      const isMobile = window.innerWidth < mdMinSize;
      setIsMobileSize(isMobile);

      return isMobile && setCollapsed(true);
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="flex h-full flex-col justify-between bg-[#515055] !text-white">
      <div className="">
        <div
          className="ml-[3px] cursor-pointer !px-5 !py-[15.5px]"
          onClick={collapseMenu}
        >
          <MenuOutlined className="text-[30px]" />
        </div>

        <StyledMenu
          mode="inline"
          inlineCollapsed={collapsed}
          style={{ borderRight: 0, backgroundColor: '#515055' }}
          theme="dark"
          items={menuItems}
          defaultSelectedKeys={[pathname]}
          selectedKeys={[pathname]}
        />
      </div>

      <div className="p-4">
        {!collapsed && (
          <Input
            placeholder={t('sidebar.searchPlaceholder')}
            suffix={<SearchOutlined />}
          />
        )}
      </div>
    </div>
  );
};

export default Sidebar;
