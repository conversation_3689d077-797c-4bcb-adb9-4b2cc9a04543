import Header from '@/components/organisms/Header';
import Sidebar from '@/components/organisms/Sidebar';
import { ReactNode, useState } from 'react';

interface MainLayoutPropsI {
  children: ReactNode;
}

function MainLayout({ children }: MainLayoutPropsI) {
  const [collapsed, setCollapsed] = useState(false);

  return (
    <div className="flex h-screen overflow-hidden">
      <aside
        className={`flex flex-col justify-between transition-all duration-300 ${
          collapsed ? 'w-20' : 'w-72'
        }`}
      >
        <Sidebar collapsed={collapsed} setCollapsed={setCollapsed} />
      </aside>

      <div className="flex min-w-0 flex-1 flex-col">
        <header className="flex items-center bg-white px-5 py-4 shadow">
          <Header />
        </header>
        <main className="flex flex-1 items-start justify-center overflow-auto bg-[var(--general-background)] p-5">
          <div className="w-full">{children}</div>
        </main>
      </div>
    </div>
  );
}

export default MainLayout;
