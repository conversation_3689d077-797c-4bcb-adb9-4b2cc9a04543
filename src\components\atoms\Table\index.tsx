import {
  type PaginationProps as AntdPaginationProps,
  Table as AntdTable,
  type TableProps as AntdTableProps,
} from 'antd';
import classNames from 'classnames';
import { useTranslation } from 'next-i18next';
import type { CSSProperties } from 'react';
import { styled } from 'styled-components';
import { Pagination } from '../Pagination';
import { EmptyTable } from './EmptyTable';

const StyledTable = styled(AntdTable)`
  border-radius: 6px;
  border: 1px solid #bbbbbb !important;
  overflow: hidden !important;

  .ant-table-thead {
    white-space: nowrap !important;
  }

  .ant-table-cell {
    padding: 8.5px 16px !important;
  }

  .custom-row:hover {
    td {
      background-color: #fcfae4 !important;
    }
  }

  .first-column-bg {
    background-color: #f6f6f6 !important;
  }
` as typeof AntdTable;

export interface TablePropsI<T extends object>
  extends Omit<AntdTableProps<T>, 'pagination'> {
  onRefresh?: () => void;
  loadingMinHeight?: CSSProperties['minHeight'];
  pagination?:
    | (AntdPaginationProps & { extraActions?: React.ReactNode })
    | false;
}

export function Table<T extends object>({
  className,
  onRefresh,
  pagination,
  loadingMinHeight = '300px',
  ...props
}: TablePropsI<T>) {
  const { t } = useTranslation('components');

  return (
    <div className={classNames('w-full space-y-[20px]', className)}>
      {pagination !== false && (
        <Pagination onRefresh={onRefresh} {...pagination} />
      )}

      <StyledTable
        locale={{
          emptyText: (
            <EmptyTable
              message={t('table.noDataFound')}
              minHeight={loadingMinHeight}
            />
          ),
        }}
        pagination={false}
        bordered
        rowClassName={() => 'custom-row'}
        scroll={{ x: 600, ...props.scroll }}
        {...props}
      />

      {pagination !== false && (
        <Pagination onRefresh={onRefresh} {...pagination} />
      )}
    </div>
  );
}
