import { apiRequest } from '@/api/api-request';
import { MAX_FILE_UPLOAD_CHUNK_SIZE } from '@/constants/common';
import { ApiEndpointE } from '@/enums';
import { UploadFileResponseI } from '@/types/api';
import { ArrayUtil } from '@/utils';
import { flatten } from 'lodash';

/**
 * The function `uploadFiles` asynchronously uploads files of specified type (image or document) using
 * FormData and API request.
 * @param {File[]} files - The `files` parameter is an array of File objects representing the files
 * that you want to upload.
 * @param {'image' | 'document'} type - The `type` parameter specifies the type of files being
 * uploaded, which can be either 'image' or 'document'.
 */
export const uploadFiles = async (
  files: File[],
  type: 'image' | 'document',
): Promise<UploadFileResponseI[]> => {
  if (!files.length) {
    return Promise.resolve([]);
  }

  const chunkedFiles = ArrayUtil.chunk(files, MAX_FILE_UPLOAD_CHUNK_SIZE);

  const res = await Promise.all(
    chunkedFiles.map(async (chunk) => {
      const formData = new FormData();
      formData.append('type', type);

      chunk.forEach((file) => {
        formData.append('files[]', file);
      });

      const response = await apiRequest.post(
        ApiEndpointE.UPLOAD_FILES,
        formData,
        {
          headers: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      return response.data;
    }),
  );

  const flattenFiles = flatten(res.map((item) => item?.result?.files));

  return Promise.resolve(flattenFiles);
};

export const getMasterCode = async (params: { code_kbn: string[] }) => {
  const response = await apiRequest.get(ApiEndpointE.MASTER_CODE, { params });

  return response.data;
};
