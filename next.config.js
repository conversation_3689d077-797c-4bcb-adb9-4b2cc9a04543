/* eslint-disable @typescript-eslint/no-require-imports */
// eslint-disable-next-line @typescript-eslint/no-require-imports
const { i18n } = require('./next-i18next.config');

const withTM = require('next-transpile-modules')([
  '@ant-design/icons',
  // '@rc-component',
  'antd',
  'rc-cascader',
  'rc-checkbox',
  'rc-collapse',
  'rc-dialog',
  'rc-drawer',
  'rc-dropdown',
  'rc-field-form',
  'rc-image',
  'rc-input',
  'rc-input-number',
  'rc-mentions',
  'rc-menu',
  'rc-motion',
  'rc-notification',
  'rc-pagination',
  'rc-picker',
  'rc-progress',
  'rc-rate',
  'rc-resize-observer',
  'rc-segmented',
  'rc-select',
  'rc-slider',
  'rc-steps',
  'rc-switch',
  'rc-table',
  'rc-tabs',
  'rc-textarea',
  'rc-tooltip',
  'rc-tree',
  'rc-tree-select',
  'rc-upload',
  'rc-util',
]);

module.exports = withTM({
  i18n,
  reactStrictMode: true,
  experimental: {
    esmExternals: true, // Ensure external ESM modules are handled correctly
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.trycloudflare.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '**.trycloudflare.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
});
