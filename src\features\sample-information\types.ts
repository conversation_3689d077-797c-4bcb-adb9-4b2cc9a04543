export interface SampleSearchFormValuesI {
  search?: string;
  pms_sample_nos?: string;
  supplier_factory_code?: string;
  gender_category_code?: string;
  pms_sample_request_datetime?: string;
  product_category_code?: string;
  pms_plan_datetime?: string;
  supplier_code?: string;
  order_flg?: number;
  receipt_flg?: number;
  payment_flg?: number;
  pms_sample_remarks?: string;
  pms_sample_request_datetime_from?: string;
  pms_sample_request_datetime_to?: string;
}

export interface SampleMediaI {
  id: number;
  media_file: MediaFileI;
  pms_sample_media_kbn: string;
  pms_sample_media_file_seq_no: number;
  pms_sample_no: string;
}

export interface SampleI {
  id: number;
  pms_sample_no: string;
  order_flg: number;
  receipt_flg: number;
  payment_flg: number;
  gender_category_code: string;
  pms_plan_datetime: string;
  pms_planner_code: string;
  pms_sample_remarks: string | null;
  pms_sample_request_datetime: string;
  product_category_code: string;
  sample_media: SampleMediaI;
  supplier_code: string;
  supplier_factory_code: string;
  updated_at: string;
}

export interface SampleFlagUpdateI {
  id: number;
  order_flg?: number;
  receipt_flg?: number;
  payment_flg?: number;
  updated_at?: string;
}

export interface MediaFileI {
  media_file_seq_no: number;
  media_file_kbn: string;
  media_file_logical_name: string;
  media_file_physical_name: string;
  media_file_path: string;
}

export interface SampleImageI {
  id: number;
  pms_sample_no: string;
  pms_sample_media_file_seq_no: number;
  pms_sample_media_file_kbn: string;
  sort_key: number;
  media_file: MediaFileI;
}

export interface SampleFileI {
  id: number;
  pms_sample_no: string;
  pms_sample_media_file_seq_no: number;
  pms_sample_media_file_kbn: string;
  sort_key: number;
  media_file: MediaFileI;
}

export interface SampleDetailI {
  id: number;
  pms_sample_no: string;
  pms_plan_datetime: string;
  pms_planner_code: string;
  product_category_code: string;
  gender_category_code: string;
  pms_sample_request_datetime: string;
  supplier_code: string;
  supplier_factory_code: string;
  order_flg: number;
  receipt_flg: number;
  payment_flg: number;
  reference_url: string | null;
  quote_kgn: string;
  quote_kgn_unit_code: string;
  pms_sample_create_cnt: number;
  pms_sample_remarks: string;
  sample_images: SampleImageI[];
  sample_files: SampleFileI[];
  deleted_at: string | null;
  created_at: string;
  created_user_id: number;
  created_program_id: number;
  updated_at: string | null;
  updated_user_id: number;
  updated_program_id: number;
  created_by: {
    id: number;
    name: string;
  };
  updated_by: {
    id: number;
    name: string;
  };
}

export interface MasterDataI {
  id: number;
  label: string;
}
