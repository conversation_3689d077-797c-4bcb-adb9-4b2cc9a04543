import { queryClient } from '@/configs/query-client';
import { store } from '@/redux/store';
import { antdTheme } from '@/styles/antd-theme';
import '@/styles/globals.css';
import { ConfigProvider } from 'antd';
import { appWithTranslation } from 'next-i18next';
import type { AppProps } from 'next/app';
import { QueryClientProvider } from 'react-query';
import { Provider } from 'react-redux';

function App({ Component, pageProps }: AppProps) {
  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider theme={antdTheme}>
          <Component {...pageProps} />
        </ConfigProvider>
      </QueryClientProvider>
    </Provider>
  );
}

export default appWithTranslation(App);
