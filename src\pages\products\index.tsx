import { withMainLayout } from '@/hocs/withMainLayout';
import { getStaticTranslationsProps } from '@/utils/localization';
import { useTranslation } from 'react-i18next';

const PmsProductListPage = () => {
  const { t } = useTranslation('products');

  return (
    <div className="flex flex-col gap-4">
      <h1 className="text-2xl font-bold">{t('productList')}</h1>
    </div>
  );
};

export const getStaticProps = getStaticTranslationsProps(['products']);

export default withMainLayout(PmsProductListPage);
