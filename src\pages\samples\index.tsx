import {
  But<PERSON>,
  <PERSON>lapse,
  Divider,
  InputSearch,
  Table,
} from '@/components/atoms';
import { AppRouteE } from '@/enums';
import SampleSearchForm from '@/features/sample-information/components/SampleSearchForm';
import { useSampleInformation } from '@/features/sample-information/hooks/useSampleInformation';
import { SampleI } from '@/features/sample-information/types';
import { withMainLayout } from '@/hocs/withMainLayout';
import usePagination from '@/hooks/usePagination';
import { getStaticTranslationsProps } from '@/utils/localization';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/navigation';
import { Fragment, useState } from 'react';

const SampleInformation = () => {
  const router = useRouter();
  const { t } = useTranslation('sample-information');
  const { pagination, setDefaultPage } = usePagination();
  const [keyword, setKeyword] = useState('');

  const {
    data,
    isFetching,
    columns,
    refetch,
    applyFilters,
    handleSubmitFlags,
  } = useSampleInformation(
    pagination.current,
    pagination.pageSize,
    setDefaultPage,
  );

  return (
    <Fragment>
      <h1 className="mb-4 text-2xl font-bold">{t('sampleInformationList')}</h1>

      <div className="mb-4 flex items-center justify-between gap-x-4">
        <InputSearch
          placeholder={t('sampleNumberNotesInput')}
          allowClear
          enterButton={t('search')}
          value={keyword}
          prefix={<SearchOutlined />}
          maxLength={150}
          onChange={(e) => {
            const value = e.target.value;
            setKeyword(value);
            if (value === '') {
              applyFilters('', {}, 'normal');
            }
          }}
          onSearch={(value: string) => {
            applyFilters(value, {}, 'normal');
          }}
        />
        <Button
          icon={<PlusOutlined />}
          label={t('sampleInformationAddition')}
          type="primary"
          onClick={() => router.push(AppRouteE.SAMPLES_CREATE)}
        />
      </div>

      <Collapse
        items={[
          {
            key: '1',
            label: t('advancedSearch'),
            children: (
              <SampleSearchForm
                onSearch={(formValues) =>
                  applyFilters('', formValues, 'advanced')
                }
              />
            ),
          },
        ]}
        className="!mb-4"
      />

      <Divider />

      <Table<SampleI>
        columns={columns}
        dataSource={data?.data}
        rowKey="id"
        pagination={{
          ...pagination,
          total: data?.meta?.total || 0,
          extraActions: (
            <Button
              type="primary"
              danger
              className="!h-[26px] !rounded-[100px]"
              onClick={handleSubmitFlags}
              label={t('bulkUpdate')}
            />
          ),
        }}
        loading={isFetching}
        onRefresh={refetch}
        loadingMinHeight="160px"
      />
    </Fragment>
  );
};

export const getStaticProps = getStaticTranslationsProps([
  'sample-information',
]);

export default withMainLayout(SampleInformation);
