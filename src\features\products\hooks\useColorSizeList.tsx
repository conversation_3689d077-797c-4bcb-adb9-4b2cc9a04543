import { Checkbox, StyledSelect } from '@/components/atoms';
import { ApiEndpointE } from '@/enums';
import { QueryKeysE } from '@/enums/query-keys';
import { SelectE } from '@/enums/select';
import { getSelectOptions } from '@/features/sample-information/services';
import { MasterDataI } from '@/features/sample-information/types';
import { openModal } from '@/utils';
import { CaretDownOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { useState } from 'react';
import ReactDOM from 'react-dom/client';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { ColumnDefinition } from 'react-tabulator';
import ColorSizeModal from '../components/ColorSizeModal';
import { getStoreBudgetTypes } from '../services';
import { StoreBudgetTypeI } from '../types';

const useColorSizeList = () => {
  const { t } = useTranslation('products');
  const numberOfOrder = 4;
  const [dataTableMateria, setDataTableMateria] = useState<
    Array<StoreBudgetTypeI>
  >([]);

  const { isLoading } = useQuery(
    [QueryKeysE.STORE_BUDGET_TYPES],
    getStoreBudgetTypes,
    {
      onSuccess: (data) => {
        if (!data?.data?.length) {
          message.warning(t('noDataFound'));

          return;
        }
        setDataTableMateria(data.data);
      },
    },
  );

  const transformMasterData = (data: any) => {
    return Array.isArray(data?.result)
      ? data.result.map((item: MasterDataI) => ({
          label: item[SelectE.M_CODE_FIELD_CODE_NAME],
          value: item[SelectE.M_CODE_FIELD_CODE],
        }))
      : [];
  };

  const createQueryOptions = (codeKbn: string) => ({
    code_kbn: [codeKbn],
    hidden_flg: 0,
  });

  const querySize = useQuery([QueryKeysE.SELECT_OPTIONS, 'size'], async () => {
    const data = await getSelectOptions(
      `http://localhost:3000/api/${ApiEndpointE.MASTER_CODE}`,
      createQueryOptions(SelectE.M_CODE_SIZE),
    );

    return transformMasterData(data);
  });

  const queryColor = useQuery(
    [QueryKeysE.SELECT_OPTIONS, 'color'],
    async () => {
      const data = await getSelectOptions(
        `http://localhost:3000/api/${ApiEndpointE.MASTER_CODE}`,
        createQueryOptions(SelectE.M_CODE_COLOR),
      );

      return transformMasterData(data);
    },
  );

  const handleOpenDetailSizeColor = (record: StoreBudgetTypeI) => {
    const closeModal = openModal({
      title: t('sizeInformationDetails'),
      content: (
        <ColorSizeModal
          previousData={record}
          t={t}
          onClose={() => {
            closeModal();
          }}
        />
      ),
      footer: null,
    });
  };

  const renderColorFormatter = (cell: any) => {
    const record = cell.getRow().getData();
    const el = document.createElement('div');
    el.className = 'w-full flex items-center';
    const root = ReactDOM.createRoot(el);

    if (record.pms_color) {
      const hasOrder =
        record.order1 || record.order2 || record.order3 || record.order4;
      root.render(
        <span
          style={{
            color: 'var(--link-text-table)',
            cursor: !hasOrder ? 'pointer' : undefined,
            textDecoration: !hasOrder ? 'underline' : undefined,
          }}
          onClick={
            !hasOrder ? () => handleOpenDetailSizeColor(record) : undefined
          }
          className="max-w-full truncate"
        >
          {record.pms_color?.code_name}
        </span>,
      );
    } else {
      root.render(
        <StyledSelect
          value={record.pms_color_code}
          options={queryColor.data}
          className="w-full"
          suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
        />,
      );
    }

    return el;
  };

  const renderSizeFormatter = (cell: any) => {
    const record = cell.getRow().getData();
    const el = document.createElement('div');
    el.className = 'w-full flex items-center';
    const root = ReactDOM.createRoot(el);

    if (record.pms_size_code && record.pms_color) {
      const hasOrder =
        record.order1 || record.order2 || record.order3 || record.order4;
      root.render(
        <span
          style={{
            color: 'var(--link-text-table)',
            cursor: !hasOrder ? 'pointer' : undefined,
            textDecoration: !hasOrder ? 'underline' : undefined,
          }}
          onClick={
            !hasOrder ? () => handleOpenDetailSizeColor(record) : undefined
          }
          className="max-w-full truncate"
        >
          {record.pms_size_code}
        </span>,
      );
    } else {
      root.render(
        <StyledSelect
          value={record.pms_color_code}
          options={querySize.data}
          className="w-full"
          suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
        />,
      );
    }

    return el;
  };

  const columnsMaterial: ColumnDefinition[] = [
    { title: t('no'), field: 'id', width: 50, hozAlign: 'center' },
    {
      title: t('color'),
      field: 'color',
      width: 200,
      formatter: renderColorFormatter,
    },
    {
      title: t('size'),
      field: 'size',
      width: 200,
      formatter: renderSizeFormatter,
    },
    ...Array.from({ length: numberOfOrder }, (_, i) => ({
      title: t('order', { count: i + 1 }),
      field: `order${i + 1}`,
      width: 150,
      formatter: (cell: any) => {
        const record = cell.getRow().getData();
        const el = document.createElement('div');
        el.className = 'w-full flex items-center justify-center';
        const root = ReactDOM.createRoot(el);

        root.render(
          <Checkbox
            type="checkbox"
            checked={record[`order${i + 1}`]}
            disabled
          />,
        );

        return el;
      },
    })),
  ];

  const handleAddRow = () => {
    const defaultColor = queryColor.data?.[0].value;
    const defaultSize = querySize.data?.[0].value;

    setDataTableMateria((prevData) => [
      ...prevData,
      {
        id: prevData.length + 1,
        pms_color_code: defaultColor,
        pms_size_code: defaultSize,
      },
    ]);
  };

  return {
    isLoading,
    dataTableMateria,
    columnsMaterial,
    handleAddRow,
    setDataTableMateria,
  };
};

export default useColorSizeList;
