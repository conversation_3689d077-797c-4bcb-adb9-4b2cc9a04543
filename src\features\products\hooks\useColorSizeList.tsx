import { Checkbox, StyledSelect } from '@/components/atoms';
import { openModal } from '@/utils';
import { CaretDownOutlined } from '@ant-design/icons';
import { useState } from 'react';
import ReactDOM from 'react-dom/client';
import { useTranslation } from 'react-i18next';
import { ColumnDefinition } from 'react-tabulator';
import ColorSizeModal from '../components/ColorSizeModal';

const useColorSizeList = () => {
  const { t } = useTranslation('products');
  const numberOfOrder = 4;
  const [dataTableMateria, setDataTableMateria] = useState([
    {
      id: 1,
      color: '襟',
      isExisting: true,
      order1: true,
      order2: true,
      order3: true,
      order4: false,
    },
    {
      id: 2,
      color: '襟',
      isExisting: true,
      order1: true,
      order2: false,
      order3: true,
      order4: false,
    },
    {
      id: 3,
      color: '襟',
      isExisting: true,
      order1: true,
      order2: true,
      order3: true,
      order4: true,
    },
  ]);

  const handleOpenDetailSizeColor = () => {
    openModal({
      title: t('sizeInformationDetails'),
      content: <ColorSizeModal t={t} />,
      footer: null,
    });
  };

  const renderColorFormatter = (cell: any) => {
    const record = cell.getRow().getData();
    const el = document.createElement('div');
    el.className = 'w-full flex items-center';
    const root = ReactDOM.createRoot(el);

    if (record.isExisting) {
      root.render(
        <span
          style={{
            color: 'var(--link-text-table)',
            cursor: 'pointer',
            textDecoration: 'underline',
          }}
          onClick={handleOpenDetailSizeColor}
          className="max-w-full truncate"
        >
          1
        </span>,
      );
    } else {
      root.render(
        <StyledSelect
          value={1}
          options={[{ value: 1, label: '1' }]}
          className="w-full"
          suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
        />,
      );
    }

    return el;
  };

  const renderSizeFormatter = (cell: any) => {
    const record = cell.getRow().getData();
    const el = document.createElement('div');
    el.className = 'w-full flex items-center';
    const root = ReactDOM.createRoot(el);

    if (record.isExisting) {
      root.render(
        <span
          style={{
            color: 'var(--link-text-table)',
            cursor: 'pointer',
            textDecoration: 'underline',
          }}
          onClick={handleOpenDetailSizeColor}
          className="max-w-full truncate"
        >
          13
        </span>,
      );
    } else {
      root.render(
        <StyledSelect
          value={1}
          options={[{ value: 1, label: '1' }]}
          className="w-full"
          suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
        />,
      );
    }

    return el;
  };

  const columnsMaterial: ColumnDefinition[] = [
    { title: t('no'), field: 'id', width: 50, hozAlign: 'center' },
    {
      title: t('color'),
      field: 'color',
      width: 200,
      formatter: renderColorFormatter,
    },
    {
      title: t('size'),
      field: 'size',
      width: 200,
      formatter: renderSizeFormatter,
    },
    ...Array.from({ length: numberOfOrder }, (_, i) => ({
      title: t('order', { count: i + 1 }),
      field: `order${i + 1}`,
      width: 150,
      formatter: (cell: any) => {
        const record = cell.getRow().getData();
        const el = document.createElement('div');
        el.className = 'w-full flex items-center justify-center';
        const root = ReactDOM.createRoot(el);

        root.render(
          <Checkbox
            type="checkbox"
            checked={record[`order${i + 1}`]}
            disabled
          />,
        );

        return el;
      },
    })),
  ];

  const handleAddRow = () => {
    setDataTableMateria((prevData) => [
      ...prevData,
      {
        id: prevData.length + 1,
        color: '',
        isExisting: false,
        order1: false,
        order2: false,
        order3: false,
        order4: false,
      },
    ]);
  };

  return {
    dataTableMateria,
    columnsMaterial,
    handleAddRow,
    setDataTableMateria,
  };
};

export default useColorSizeList;
