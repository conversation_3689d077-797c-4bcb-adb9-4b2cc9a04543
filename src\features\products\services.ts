import { apiRequest } from '@/api/api-request';
import { ApiEndpointE } from '@/enums';
import { ApiResponseListI } from '@/types/api';
import { StoreBudgetTypeI } from './types';

export const getStoreBudgetTypes = async (): Promise<
  ApiResponseListI<StoreBudgetTypeI>['result']
> => {
  const response = await apiRequest.get(
    `http://localhost:3000/api/${ApiEndpointE.STORE_BUDGET_TYPES}`,
  );

  return response.data.result;
};
