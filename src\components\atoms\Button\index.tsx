import { Button as AntdButton, ButtonProps as AntdButtonProps } from 'antd';
import { styled } from 'styled-components';

const StyledButton = styled(AntdButton)`
  &.ant-btn-default {
    border-color: var(--button-accent-text);
    color: var(--button-accent-text);
  }
`;

export interface ButtonPropsI extends AntdButtonProps {
  label: string;
}

export const Button = ({ label, type = 'default', ...props }: ButtonPropsI) => {
  return (
    <StyledButton className="px-4 py-2" type={type} {...props}>
      {label}
    </StyledButton>
  );
};
