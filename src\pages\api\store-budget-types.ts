import { StoreBudgetTypeI } from '@/features/products/types';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'GET') {
    await new Promise((resolve) => setTimeout(resolve, 400));

    const data: StoreBudgetTypeI[] = [
      {
        id: 1,
        t_pms_product_fkid: 1,
        pms_color_code: 'B<PERSON>C<PERSON>',
        pms_color: {
          code_kbn: 'COLOR',
          code: 'B<PERSON>C<PERSON>',
          code_name: 'BLACK',
        },
        pms_size_code: 'M',
        pms_size: null,
      },
      {
        id: 2,
        t_pms_product_fkid: 1,
        pms_color_code: 'B<PERSON><PERSON><PERSON>',
        pms_color: {
          code_kbn: 'COLOR',
          code: '<PERSON><PERSON><PERSON><PERSON>',
          code_name: '<PERSON><PERSON><PERSON><PERSON>',
        },
        pms_size_code: 'L',
        pms_size: null,
      },
    ];
    res.status(200).json({
      status: 200,
      result: {
        data,
      },
      message: 'データの取得に成功しました。',
    });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
