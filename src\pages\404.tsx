import { Image } from '@/components/atoms';
import { getStaticTranslationsProps } from '@/utils/localization';
import type { GetStaticProps } from 'next';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';

export default function Custom404() {
  const { t } = useTranslation('common');

  return (
    <div className="flex min-h-screen items-center justify-center bg-[var(--stripe-caret-background)] p-4">
      <div className="h-[768px] w-[1366px] rounded-4xl bg-white px-6 pb-10 text-center shadow-lg">
        <Image
          src="/images/404.avif"
          alt="404"
          width={591}
          height={420}
          className="mx-auto"
        />
        <p className="mt-32 mb-12 text-base text-[17px] leading-9 font-semibold tracking-wider uppercase">
          {t('404.title')}
        </p>
        <Link
          href="/"
          className="inline-block h-10 rounded-[8px] bg-[var(--button-accent-text)] px-4 text-center leading-10 text-white uppercase shadow-md"
        >
          {t('404.backToHome')}
        </Link>
      </div>
    </div>
  );
}

export const getStaticProps: GetStaticProps = getStaticTranslationsProps();
