import { FormFieldConfigI } from '@/components/organisms/Form';
import { ApiEndpointE } from '@/enums';
import { SelectE } from '@/enums/select';
import { Form as AntdForm, message } from 'antd';
import { TFunction } from 'i18next';

const useColorSizeDetail = (t: TFunction<'products'>) => {
  const [form] = AntdForm.useForm();
  const formFields: FormFieldConfigI[] = [
    {
      label: t('color'),
      name: 'color',
      type: 'select',
      apiConfig: {
        endpoint: `http://localhost:3000/api/${ApiEndpointE.MASTER_CODE}`,
        params: { code_kbn: [SelectE.M_CODE_COLOR], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      rules: [{ required: true, message: t('pleaseEnterRequiredFields') }],
    },
    {
      label: t('size'),
      name: 'size',
      type: 'select',
      apiConfig: {
        endpoint: `http://localhost:3000/api/${ApiEndpointE.MASTER_CODE}`,
        params: { code_kbn: [SelectE.M_CODE_SIZE], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      rules: [{ required: true, message: t('pleaseEnterRequiredFields') }],
    },
  ];
  const detail = {
    created_at: '2023/03/14 13:38:57',
    created_by: { name: 'SYSTEM システムサポート)' },
    updated_at: '2024/12/16 11:56:41',
    updated_by: { name: 'kenji_tsuboi 坪井 建治' },
  };

  const handleSubmit = async () => {
    await form.validateFields();
    // Implement edit logic here
    message.success(t('updateCompleted'));
  };

  const handleDelete = async () => {
    // Implement delete logic here
    message.success(t('deleteCompleted'));
  };

  return { form, formFields, detail, handleSubmit, handleDelete };
};

export default useColorSizeDetail;
