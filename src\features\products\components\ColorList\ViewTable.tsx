interface ViewTablePropsI {
  data: Array<{ label: string; value: string }>;
}

const ViewTable = ({ data }: ViewTablePropsI) => {
  return (
    <div className="flex w-full flex-col divide-y divide-[#BBBBBB] border border-[#BBBBBB]">
      {data.map((item, idx) => (
        <div
          key={idx}
          className="flex w-full flex-row divide-x divide-[#BBBBBB]"
        >
          <div
            className={`flex w-[150px] min-w-[150px] grow-0 items-center justify-end bg-[#F6F6F6] px-3 py-2 font-semibold`}
          >
            <span className="max-w-full truncate">{item.label}</span>
          </div>
          <div className="relative grow bg-white">
            <div className="absolute inset-0 flex h-full w-full items-center justify-start px-3 py-2">
              <span className="max-w-full truncate">{item.value}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ViewTable;
