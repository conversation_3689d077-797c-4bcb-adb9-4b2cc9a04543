import { Select } from '@/components/atoms';
import useLocalization from '@/hooks/useLocalization';
import { useTranslation } from 'next-i18next';
import { EnglandFlagIcon, JanpanFlagIcon } from '../../../../public/icons';

const Header = () => {
  const { i18n, t } = useTranslation('components');

  const { changeLanguage } = useLocalization();

  return (
    <div className="flex w-full flex-wrap items-center justify-end gap-y-[12px]">
      <div className="flex flex-wrap items-center gap-x-4">
        <div className="flex flex-wrap items-center gap-x-2">
          <div className="text-sm text-gray-500">{t('header.companyName')}</div>
          <div className="font-semibold">{t('header.userName')}</div>
        </div>

        <Select
          value={i18n.language}
          onChange={(value) => changeLanguage(value)}
          style={{ width: 140 }}
          options={[
            {
              value: 'ja',
              label: (
                <div className="flex items-center gap-x-2">
                  <JanpanFlagIcon className="h-6 w-6" />
                  <span>Janpanese</span>
                </div>
              ),
            },
            {
              value: 'en',
              label: (
                <div className="flex items-center gap-x-2">
                  <EnglandFlagIcon className="h-6 w-6" />
                  <span>English</span>
                </div>
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};

export default Header;
