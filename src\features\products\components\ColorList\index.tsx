import { Button, EditableTable } from '@/components/atoms';
import { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import useColorSizeList from '../../hooks/useColorSizeList';
import InfoProduct from './ViewTable';

const ColorList = () => {
  const { t } = useTranslation('products');
  const {
    dataTableMateria,
    columnsMaterial,
    handleAddRow,
    setDataTableMateria,
  } = useColorSizeList();

  return (
    <Fragment>
      <div className="flex w-full flex-col gap-4">
        <InfoProduct
          data={[
            { label: t('partNumber'), value: '36-250134302-10' },
            {
              label: t('productName'),
              value: 'キッズ　配色ステッチ刺繍半袖Tシャツ',
            },
          ]}
        />
        <div className="flex w-full flex-col gap-1">
          <div className="flex items-center justify-between">
            <span>{t('noteCheckColorSize')}</span>
            <Button label={t('addRow')} type="primary" onClick={handleAddRow} />
          </div>
          <EditableTable
            pagination={false}
            data={dataTableMateria}
            columns={columnsMaterial}
            setData={setDataTableMateria}
            headerAlign="center"
          />
        </div>
      </div>
    </Fragment>
  );
};

export default ColorList;
