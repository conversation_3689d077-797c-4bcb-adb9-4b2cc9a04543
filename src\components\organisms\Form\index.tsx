import {
  Button,
  Checkbox,
  DatePicker,
  EditableTable,
  Input,
  InputPassword,
  Radio,
  Select,
  TableOptionsI,
  TextArea,
} from '@/components/atoms';
import { UploadOutlined } from '@ant-design/icons';
import { Form as AntdForm, Col, FormProps, Row } from 'antd';
import { Rule } from 'antd/es/form';
import { Dayjs } from 'dayjs';
import { useTranslation } from 'next-i18next';

import { ColumnDefinition } from 'react-tabulator';
import {
  StyledFieldInput,
  StyledFieldLabel,
  StyledFormContainer,
  StyledFormField,
} from './styled';

interface FormFieldOptionI {
  value: string | number;
  label: string;
}

export interface FormFieldConfigI {
  name: string;
  label?: string;
  labelButton?: string;
  type:
    | 'input'
    | 'password'
    | 'textarea'
    | 'datepicker'
    | 'select'
    | 'checkbox'
    | 'radiogroup'
    | 'datepicker-range'
    | 'checkboxgroup'
    | 'empty'
    | 'editable-table'
    | 'field-group'
    | 'button'
    | 'upload';
  options?: FormFieldOptionI[];
  labelColSpan?: number;
  labelPosition?: 'left' | 'right' | 'center';
  rules?: Rule[];
  placeholder?: string | [string, string];
  disabled?: boolean;
  rows?: number;
  initialValue?:
    | string
    | number
    | boolean
    | null
    | Dayjs
    | [Dayjs | null, Dayjs | null]
    | Array<string | number>;
  editableTableConfig?: {
    data: any[];
    columns: ColumnDefinition[];
    tableOptions?: TableOptionsI;
    setData: (data: any[]) => void;
  };
  fields?: FormFieldConfigI[];
  style?: React.CSSProperties;
  picker?: 'date' | 'week' | 'month' | 'quarter' | 'year';
  format?: string;
  extra?: string | React.ReactNode;
  apiConfig?: {
    endpoint: string;
    params?: Record<string, any>;
    mapFields: {
      label: string;
      value: string;
    };
  };
  maxLength?: number;
  render?: () => React.ReactNode;
}

export interface FormPropsI extends FormProps {
  formFields: FormFieldConfigI[];
  numOfColumns?: number;
}

const FormField = ({
  label,
  name,
  type,
  options,
  labelColSpan,
  labelPosition,
  rules,
  placeholder,
  disabled,
  rows = 1,
  editableTableConfig,
  fields,
  labelButton,
  style = {},
  apiConfig,
  ...rest
}: FormFieldConfigI) => {
  const { t } = useTranslation('components');
  const renderField = () => {
    if (type === 'field-group' && Array.isArray(fields)) {
      return (
        <div style={{ display: 'flex', gap: 8 }}>
          {fields.map((f: FormFieldConfigI) => (
            <div
              key={f.name}
              style={{ flex: f.type === 'button' ? 'none' : 1, minWidth: 0 }}
            >
              <FormField
                {...f}
                labelColSpan={0}
                labelPosition="left"
                style={{ padding: 0 }}
              />
            </div>
          ))}
        </div>
      );
    }

    switch (type) {
      case 'textarea':
        return <TextArea rows={rows} className="w-full" />;
      case 'datepicker':
        return (
          <DatePicker
            className="w-full"
            placeholder={
              typeof placeholder === 'string' ? placeholder : undefined
            }
            {...rest}
          />
        );
      case 'select':
        return (
          <Select apiConfig={apiConfig} options={options} className="w-full" />
        );
      case 'checkbox':
        return <Checkbox label="" />;
      case 'radiogroup':
        return <Radio type="group" options={options} />;
      case 'upload':
        return <Button icon={<UploadOutlined />} label={t('form.upload')} />;
      case 'checkboxgroup':
        return <Checkbox.Group options={options} />;
      case 'password':
        return <InputPassword />;
      case 'datepicker-range':
        return rest.render ? rest.render() : null;
      case 'empty':
        return null;
      case 'editable-table':
        return editableTableConfig ? (
          <EditableTable pagination={false} {...editableTableConfig} />
        ) : null;
      case 'input':
        return <Input disabled={disabled} maxLength={rest?.maxLength} />;
      case 'button':
        return <Button label={labelButton ?? ''} className="float-right" />;
    }
  };

  return (
    <StyledFormField>
      <StyledFieldLabel
        hidden={!label}
        labelColSpan={labelColSpan}
        labelPosition={labelPosition}
      >
        {label}
      </StyledFieldLabel>
      <StyledFieldInput style={style}>
        <AntdForm.Item
          name={name}
          rules={rules}
          valuePropName={type === 'checkbox' ? 'checked' : 'value'}
          {...rest}
        >
          {renderField()}
        </AntdForm.Item>
      </StyledFieldInput>
    </StyledFormField>
  );
};

export const Form = ({ formFields, numOfColumns = 3, ...rest }: FormPropsI) => {
  const renderFormFields = () => {
    const rows = [];
    const colSpan = 24 / numOfColumns;
    for (let i = 0; i < formFields.length; i += numOfColumns) {
      const chunk = formFields.slice(i, i + numOfColumns);
      rows.push(
        <Row key={i}>
          {chunk.map((field) => (
            <Col span={colSpan} key={field.name}>
              <FormField {...field} />
            </Col>
          ))}
          {chunk.length < numOfColumns && (
            <Col span={24 - chunk.length * colSpan}>
              <StyledFormField>
                <StyledFieldInput style={{ width: '100%' }} />
              </StyledFormField>
            </Col>
          )}
        </Row>,
      );
    }

    return rows;
  };

  return (
    <StyledFormContainer>
      <AntdForm {...rest}>{renderFormFields()}</AntdForm>
    </StyledFormContainer>
  );
};
