export const DEFAULT_PAGE_SIZE = 10;

export const DEFAULT_PAGE = 1;

export const PAGE_SIZE_OPTIONS = [
  { value: 10, label: '10' },
  { value: 20, label: '20' },
  { value: 50, label: '50' },
  { value: 100, label: '100' },
];

export const IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/bmp',
  'image/webp',
  'image/svg+xml',
  'image/tiff',
  'image/x-icon',
  'image/avif',
];

export const IMAGE_EXTENSIONS = [
  'jpeg',
  'jpg',
  'png',
  'gif',
  'bmp',
  'webp',
  'svg',
  'tiff',
  'ico',
  'avif',
];

export const FILE_EXTENSIONS = ['pdf', 'docx', 'xlsx', 'pptx'];

export const FILE_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];

export const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB

export const MAX_FILE_UPLOAD_CHUNK_SIZE = 5;

export const MAX_LABEL_LENGTH = 30;
