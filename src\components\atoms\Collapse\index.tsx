import { CaretRightOutlined } from '@ant-design/icons';
import { Collapse as AntCollapse, CollapseProps } from 'antd';
import React from 'react';
import styled from 'styled-components';

const StyledCollapse = styled(AntCollapse)`
  background-color: white !important;

  .ant-collapse-header {
    font-weight: 700;
  }
`;

export const Collapse: React.FC<CollapseProps> = (props) => {
  return (
    <StyledCollapse
      expandIcon={({ isActive }) => (
        <CaretRightOutlined rotate={isActive ? 90 : 0} />
      )}
      {...props}
    />
  );
};
