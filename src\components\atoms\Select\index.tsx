import { QueryKeysE } from '@/enums/query-keys';
import { getSelectOptions } from '@/features/sample-information/services';
import { CaretDownOutlined } from '@ant-design/icons';
import { Select as AntdSelect, SelectProps as AntdSelectProps } from 'antd';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { styled } from 'styled-components';

export const StyledSelect = styled(AntdSelect)`
  .ant-select-selector {
    box-shadow: var(--inner-box-shadow) !important;
  }
` as typeof AntdSelect;

export interface SelectPropsI extends AntdSelectProps {
  apiConfig?: {
    endpoint: string;
    params?: Record<string, any>;
    mapFields: {
      label: string;
      value: string;
    };
  };
}

export const Select = (props: SelectPropsI) => {
  const { apiConfig } = props;
  const mapFields = apiConfig?.mapFields;

  const query = useQuery(
    [QueryKeysE.SELECT_OPTIONS, apiConfig],
    async () => {
      const data = await getSelectOptions(
        String(apiConfig?.endpoint),
        apiConfig?.params,
      );
      if (Array.isArray(data?.result) && mapFields) {
        return data.result.map((item: any) => ({
          label: item[mapFields.label],
          value: item[mapFields.value],
        }));
      }
    },
    { enabled: !!apiConfig },
  );

  const options = useMemo(
    () => query.data || props.options || [],
    [query.data, props.options],
  );

  const styledSelectProp = { ...props, options };

  return (
    <StyledSelect
      className="w-full"
      suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
      {...styledSelectProp}
    />
  );
};
