import classNames from 'classnames';
import React from 'react';

const variantClasses = {
  h1: 'text-4xl font-bold',
  h2: 'text-3xl font-bold',
  h3: 'text-2xl font-bold',
  h4: 'text-xl font-bold',
  h5: 'text-lg font-bold',
  h6: 'text-base font-bold',
  p: 'text-base',
  body1: 'text-base',
  body2: 'text-sm',
  span: '',
  caption: 'text-xs text-gray-500',
};

const variantTags: { [key: string]: React.ElementType } = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  h5: 'h5',
  h6: 'h6',
  p: 'p',
  body1: 'p',
  body2: 'p',
  span: 'span',
  caption: 'span',
};

export type TextVariantT = keyof typeof variantClasses;

export interface TextPropsI {
  children: React.ReactNode;
  as?: React.ElementType;
  variant?: TextVariantT;
  className?: string;
  [key: string]: any;
}

export const Text = ({
  children,
  as,
  variant = 'p',
  className,
  ...props
}: TextPropsI) => {
  const Component = as || variantTags[variant] || 'p';

  return (
    <Component
      className={classNames(variantClasses[variant], className)}
      {...props}
    >
      {children}
    </Component>
  );
};
