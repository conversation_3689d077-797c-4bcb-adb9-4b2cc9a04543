.custom-scrollbar {
  scrollbar-width: 2px;
  scrollbar-color: var(--icon-sub-button) transparent;

  &::-webkit-scrollbar {
    width: 3px; /* Adjust vertical scrollbar width */
    height: 6px; /* Adjust horizontal scrollbar height */
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--general-background);
    border-radius: 3px;
    border: 2px solid transparent; /* Creates spacing around thumb */
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--general-background);
  }
}

.max-3-rows-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
