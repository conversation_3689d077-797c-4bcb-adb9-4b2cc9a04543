import { withMainLayout } from '@/hocs/withMainLayout';
import { getStaticTranslationsProps } from '@/utils/localization';
import dynamic from 'next/dynamic';
import { Fragment } from 'react';

const ColorList = dynamic(
  () => import('@/features/products/components/ColorList'),
  { ssr: false },
);

const PmsProductCreatePage = () => {
  return (
    <Fragment>
      <ColorList />
    </Fragment>
  );
};

export const getStaticProps = getStaticTranslationsProps(['products']);

export default withMainLayout(PmsProductCreatePage);
