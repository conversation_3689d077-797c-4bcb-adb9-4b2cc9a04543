import { Button, Checkbox, Image } from '@/components/atoms';
import { AppRouteE } from '@/enums';
import { QueryKeysE } from '@/enums/query-keys';
import {
  getSamples,
  updateSampleFlags,
} from '@/features/sample-information/services';
import { DateHelper } from '@/utils';
import { Flex, message } from 'antd';
import { ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/navigation';
import { useRef, useState } from 'react';
import { useQuery } from 'react-query';
import { SampleFormFieldKeyE } from '../enums';
import { SampleFlagUpdateI, SampleI } from '../types';

export const useSampleInformation = (
  page: number,
  perPage: number,
  setDefaultPage: () => void,
) => {
  const { t } = useTranslation('sample-information');
  const router = useRouter();
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [changedFlags, setChangedFlags] = useState<
    Record<number, Partial<SampleI>>
  >({});

  const originalFlagsRef = useRef<Record<number, Partial<SampleFlagUpdateI>>>(
    {},
  );

  const query = useQuery(
    [QueryKeysE.SAMPLES, page, perPage, filters, searchKeyword],
    () =>
      getSamples(page, perPage, {
        ...filters,
        search: searchKeyword,
      }),
    {
      onSuccess: (data) => {
        if (!data?.data?.length) {
          message.warning(t('noDataFound'));

          return;
        }
        const original: Record<number, Partial<SampleFlagUpdateI>> = {};
        data.data.forEach((item) => {
          original[item.id] = {
            order_flg: item.order_flg,
            receipt_flg: item.receipt_flg,
            payment_flg: item.payment_flg,
            updated_at: item.updated_at,
          };
        });
        originalFlagsRef.current = original;
      },
    },
  );

  const applyFilters = (
    keyword: string,
    advancedFilters: Record<string, any>,
    mode: 'normal' | 'advanced' = 'normal',
  ) => {
    setDefaultPage();
    if (mode === 'normal') {
      setSearchKeyword(keyword.trim());
      setFilters({});
    } else {
      setSearchKeyword('');
      setFilters(advancedFilters);
    }
  };

  const handleCheckboxChange = (
    id: number,
    key:
      | SampleFormFieldKeyE.ORDER
      | SampleFormFieldKeyE.RECEIPT
      | SampleFormFieldKeyE.PAYMENT,
    value: boolean,
  ) => {
    const numericValue = value ? 1 : 0;
    const originalValue = originalFlagsRef.current[id]?.[key];

    if (originalValue === numericValue) {
      setChangedFlags((prev) => {
        const updated = { ...prev };
        if (updated[id]) {
          delete updated[id][key];
          if (Object.keys(updated[id]).length === 1) {
            delete updated[id];
          }
        }

        return updated;
      });
    } else {
      setChangedFlags((prev) => {
        const originalUpdatedAt = originalFlagsRef.current[id]?.updated_at;
        const formattedUpdatedAt = originalUpdatedAt
          ? new Date(originalUpdatedAt)
              .toISOString()
              .slice(0, 19)
              .replace('T', ' ')
          : '';

        return {
          ...prev,
          [id]: {
            ...prev[id],
            id,
            [key]: numericValue,
            updated_at: formattedUpdatedAt,
          },
        };
      });
    }
  };

  const handleSubmitFlags = async () => {
    const flags: SampleFlagUpdateI[] = Object.values(changedFlags).filter(
      (flag): flag is SampleFlagUpdateI => typeof flag.id === 'number',
    );

    if (flags.length === 0) {
      message.info(t('noDataToUpdate'));

      return;
    }

    try {
      await updateSampleFlags({ flags });
      setChangedFlags({});
      query.refetch();
      message.success(t('updateCompleted'));
    } catch {
      message.error(t('updateFailed'));
    }
  };

  const columns: ColumnType<SampleI>[] = [
    {
      title: t('no'),
      key: SampleFormFieldKeyE.NO,
      width: 70,
      fixed: 'left',
      render: (_: any, _record: SampleI, index: number) => index + 1,
      align: 'center',
      className: 'first-column-bg',
    },
    {
      title: t('sampleInformation'),
      dataIndex: SampleFormFieldKeyE.COPY,
      key: SampleFormFieldKeyE.COPY,
      width: 170,
      align: 'center',
      fixed: 'left',
      render: () => (
        <Button
          label={t('copy')}
          type="primary"
          className="border-none text-white"
        />
      ),
    },
    {
      title: t('image'),
      dataIndex: SampleFormFieldKeyE.IMAGE,
      key: SampleFormFieldKeyE.IMAGE,
      width: 120,
      align: 'center',
      fixed: 'left',
      render: (_: any, record: SampleI) => (
        <Flex justify="center" align="center">
          <Image
            width={80}
            height={80}
            src={record.sample_media?.media_file?.media_file_path}
            alt="sample"
          />
        </Flex>
      ),
    },
    {
      title: t('sampleNumber'),
      dataIndex: SampleFormFieldKeyE.SAMPLE_NUMBER,
      key: SampleFormFieldKeyE.SAMPLE_NUMBER,
      width: 150,
      fixed: 'left',
      render: (value: string, record: SampleI) => (
        <span
          style={{
            color: 'var(--link-text-table)',
            cursor: 'pointer',
            textDecoration: 'underline',
          }}
          onClick={() => router.push(`/${AppRouteE.SAMPLES}/${record.id}`)}
        >
          {value}
        </span>
      ),
      align: 'center',
    },
    {
      title: t('orderTarget'),
      dataIndex: SampleFormFieldKeyE.ORDER,
      key: SampleFormFieldKeyE.ORDER,
      width: 100,
      align: 'center',
      render: (checked: boolean, record: SampleI) => (
        <Checkbox
          defaultChecked={checked}
          onChange={(e) =>
            handleCheckboxChange(
              record.id,
              SampleFormFieldKeyE.ORDER,
              e.target.checked,
            )
          }
        />
      ),
    },
    {
      title: t('receivedRefund'),
      dataIndex: SampleFormFieldKeyE.RECEIPT,
      key: SampleFormFieldKeyE.RECEIPT,
      width: 100,
      align: 'center',
      render: (checked: boolean, record: SampleI) => (
        <Checkbox
          defaultChecked={checked}
          onChange={(e) =>
            handleCheckboxChange(
              record.id,
              SampleFormFieldKeyE.RECEIPT,
              e.target.checked,
            )
          }
        />
      ),
    },
    {
      title: t('paid'),
      dataIndex: SampleFormFieldKeyE.PAYMENT,
      key: SampleFormFieldKeyE.PAYMENT,
      width: 100,
      align: 'center',
      render: (checked: boolean, record: SampleI) => (
        <Checkbox
          defaultChecked={checked}
          onChange={(e) =>
            handleCheckboxChange(
              record.id,
              SampleFormFieldKeyE.PAYMENT,
              e.target.checked,
            )
          }
        />
      ),
    },
    {
      title: t('gender'),
      width: 120,
      dataIndex: SampleFormFieldKeyE.GENDER_CATEGORY,
      key: SampleFormFieldKeyE.GENDER_CATEGORY,
      align: 'center',
      render: (value: string) => <p className="text-left">{value}</p>,
    },
    {
      title: t('productCategory'),
      width: 120,
      dataIndex: SampleFormFieldKeyE.PRODUCT_CATEGORY,
      key: SampleFormFieldKeyE.PRODUCT_CATEGORY,
      align: 'center',
      render: (value: string) => (
        <span className="max-3-rows-truncate text-left">{value}</span>
      ),
    },
    {
      title: t('deliveryDate'),
      width: 120,
      dataIndex: SampleFormFieldKeyE.REQUEST_DATE,
      key: SampleFormFieldKeyE.REQUEST_DATE,
      align: 'center',
      render: (value: string) =>
        value ? dayjs(value).format(DateHelper.FORMAT.FORMAT_2) : '',
    },
    {
      title: t('customer'),
      width: 120,
      dataIndex: SampleFormFieldKeyE.PARTNER,
      key: SampleFormFieldKeyE.PARTNER,
      align: 'center',
      render: (value: string) => (
        <span className="max-3-rows-truncate text-left">{value}</span>
      ),
    },
    {
      title: t('buyer'),
      width: 120,
      dataIndex: SampleFormFieldKeyE.PLANNER,
      key: SampleFormFieldKeyE.PLANNER,
      align: 'center',
      render: (value: string) => <p className="text-left">{value}</p>,
    },
    {
      title: t('notes'),
      width: 250,
      dataIndex: SampleFormFieldKeyE.PREPARATION,
      key: SampleFormFieldKeyE.PREPARATION,
      render: (value: string) => (
        <span className="max-3-rows-truncate text-left">{value}</span>
      ),
    },
  ];

  return { ...query, columns, applyFilters, handleSubmitFlags };
};
