{"name": "cosmos-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:mac": "chmod +x ./scripts/start-dev.sh && ./scripts/start-dev.sh", "build": "next build", "start": "rm -rf .next && next build && next start -p 5000", "lint": "next lint", "lint:fix": "eslint . --fix", "lint:commit": "commitlint --from=HEAD~1", "format": "prettier --write . --log-level silent", "prepare": "husky", "merge-db": "./scripts/merge-db.sh", "merge-db:mac": "chmod +x ./scripts/merge-db.sh && ./scripts/merge-db.sh", "storybook": "storybook dev -p 6006", "build:storybook": "storybook build", "test": "vitest run --coverage", "test:watch": "vitest --coverage"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^6.0.0", "@faker-js/faker": "^9.8.0", "@reduxjs/toolkit": "^2.8.2", "@vitejs/plugin-react": "^4.5.2", "antd": "^5.1.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "i18next": "^25.2.1", "json-server": "0.17.3", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "next": "13.4.7", "next-i18next": "^15.4.2", "next-transpile-modules": "^10.0.1", "path-to-regexp": "^8.2.0", "query-string": "^9.1.2", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-if": "^4.1.5", "react-query": "^3.39.3", "react-redux": "^9.2.0", "react-tabulator": "^0.21.0", "redux-deep-persist": "^1.0.7", "redux-persist": "^6.0.0", "sass": "^1.89.0", "secure-json-parse": "^4.0.0", "styled-components": "^6.1.18", "tabulator-tables": "^6.3.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@chromatic-com/storybook": "^4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.27.0", "@storybook/addon-a11y": "^7.6.17", "@storybook/addon-docs": "^7.6.17", "@storybook/addon-onboarding": "^9.0.0", "@storybook/addon-vitest": "^9.0.0", "@storybook/nextjs": "^7.6.17", "@storybook/react-vite": "^9.0.12", "@tailwindcss/postcss": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitest/browser": "^3.2.3", "@vitest/coverage-v8": "^3.2.3", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^8", "eslint-config-next": "14.2.28", "eslint-config-prettier": "^10.1.5", "eslint-plugin-filename-rules": "^1.3.1", "eslint-plugin-storybook": "9.0.6", "husky": "^9.1.7", "jsdom": "^26.1.0", "playwright": "^1.52.0", "postcss": "^8.5.4", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-sh": "^0.17.4", "prettier-plugin-tailwindcss": "^0.6.11", "storybook": "^9.0.6", "tailwindcss": "^4.1.8", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.3"}}